import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  Dimensions,
  SafeAreaView,
  ImageBackground,
  LinearGradient,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient as ExpoLinearGradient } from 'expo-linear-gradient';
import { COLORS, APP_CONFIG } from '../constants';
import { Artisan, EcoSite, Tour } from '../types';
import { getArtisans, getEcoSites, getTours } from '../services/supabase';

const { width } = Dimensions.get('window');

interface HomeScreenProps {
  navigation: any;
}

const HomeScreen: React.FC<HomeScreenProps> = ({ navigation }) => {
  const [featuredArtisans, setFeaturedArtisans] = useState<Artisan[]>([]);
  const [featuredSites, setFeaturedSites] = useState<EcoSite[]>([]);
  const [featuredTours, setFeaturedTours] = useState<Tour[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadFeaturedContent();
  }, []);

  const loadFeaturedContent = async () => {
    try {
      setLoading(true);
      
      // Load featured content (top rated items)
      const [artisansResult, sitesResult, toursResult] = await Promise.all([
        getArtisans(),
        getEcoSites(),
        getTours(),
      ]);

      if (artisansResult.data) {
        setFeaturedArtisans(artisansResult.data.slice(0, 5));
      }
      
      if (sitesResult.data) {
        setFeaturedSites(sitesResult.data.slice(0, 5));
      }
      
      if (toursResult.data) {
        setFeaturedTours(toursResult.data.slice(0, 5));
      }
    } catch (error) {
      console.error('Error loading featured content:', error);
    } finally {
      setLoading(false);
    }
  };

  const renderQuickActions = () => (
    <View style={styles.quickActionsContainer}>
      <Text style={styles.sectionTitle}>🌟 Découvrez la Tunisie</Text>
      <View style={styles.quickActionsGrid}>
        <TouchableOpacity
          style={[styles.quickActionCard, { backgroundColor: COLORS.primary }]}
          onPress={() => navigation.navigate('Explore')}
        >
          <ExpoLinearGradient
            colors={[COLORS.primary, '#E91E63']}
            style={styles.quickActionGradient}
          >
            <Ionicons name="compass" size={36} color="#FFFFFF" />
            <Text style={styles.quickActionText}>Explorer</Text>
            <Text style={styles.quickActionSubtext}>Sites & Lieux</Text>
          </ExpoLinearGradient>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.quickActionCard, { backgroundColor: COLORS.secondary }]}
          onPress={() => navigation.navigate('Map')}
        >
          <ExpoLinearGradient
            colors={[COLORS.secondary, '#4CAF50']}
            style={styles.quickActionGradient}
          >
            <Ionicons name="location" size={36} color="#FFFFFF" />
            <Text style={styles.quickActionText}>Carte</Text>
            <Text style={styles.quickActionSubtext}>Navigation</Text>
          </ExpoLinearGradient>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.quickActionCard, { backgroundColor: COLORS.accent }]}
          onPress={() => navigation.navigate('TourPlanner')}
        >
          <ExpoLinearGradient
            colors={[COLORS.accent, '#FFC107']}
            style={styles.quickActionGradient}
          >
            <Ionicons name="calendar" size={36} color="#FFFFFF" />
            <Text style={styles.quickActionText}>Planifier</Text>
            <Text style={styles.quickActionSubtext}>Votre Circuit</Text>
          </ExpoLinearGradient>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.quickActionCard, { backgroundColor: COLORS.nature.earth }]}
          onPress={() => navigation.navigate('Artisans')}
        >
          <ExpoLinearGradient
            colors={[COLORS.nature.earth, '#8D6E63']}
            style={styles.quickActionGradient}
          >
            <Ionicons name="hammer" size={36} color="#FFFFFF" />
            <Text style={styles.quickActionText}>Artisans</Text>
            <Text style={styles.quickActionSubtext}>Savoir-faire</Text>
          </ExpoLinearGradient>
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderFeaturedSection = (
    title: string,
    items: any[],
    onItemPress: (item: any) => void,
    renderItem: (item: any) => React.ReactNode
  ) => (
    <View style={styles.featuredSection}>
      <View style={styles.sectionHeader}>
        <Text style={styles.sectionTitle}>{title}</Text>
        <TouchableOpacity>
          <Text style={styles.seeAllText}>Voir tout</Text>
        </TouchableOpacity>
      </View>
      <ScrollView horizontal showsHorizontalScrollIndicator={false}>
        {items.map((item, index) => (
          <TouchableOpacity
            key={item.id || index}
            style={styles.featuredCard}
            onPress={() => onItemPress(item)}
          >
            {renderItem(item)}
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );

  const renderArtisanCard = (artisan: Artisan) => (
    <>
      <Image
        source={{ uri: artisan.images[0] || 'https://via.placeholder.com/200x120' }}
        style={styles.cardImage}
      />
      <View style={styles.cardContent}>
        <Text style={styles.cardTitle} numberOfLines={1}>
          {artisan.name}
        </Text>
        <Text style={styles.cardSubtitle} numberOfLines={1}>
          {artisan.specialty}
        </Text>
        <View style={styles.cardFooter}>
          <View style={styles.ratingContainer}>
            <Ionicons name="star" size={14} color={COLORS.accent} />
            <Text style={styles.ratingText}>{artisan.rating.toFixed(1)}</Text>
          </View>
          <Text style={styles.locationText}>{artisan.governorate}</Text>
        </View>
      </View>
    </>
  );

  const renderSiteCard = (site: EcoSite) => (
    <>
      <Image
        source={{ uri: site.images[0] || 'https://via.placeholder.com/200x120' }}
        style={styles.cardImage}
      />
      <View style={styles.cardContent}>
        <Text style={styles.cardTitle} numberOfLines={1}>
          {site.name}
        </Text>
        <Text style={styles.cardSubtitle} numberOfLines={1}>
          {site.type}
        </Text>
        <View style={styles.cardFooter}>
          <View style={styles.ratingContainer}>
            <Ionicons name="star" size={14} color={COLORS.accent} />
            <Text style={styles.ratingText}>{site.rating.toFixed(1)}</Text>
          </View>
          <Text style={styles.locationText}>{site.governorate}</Text>
        </View>
      </View>
    </>
  );

  const renderTourCard = (tour: Tour) => (
    <>
      <Image
        source={{ uri: tour.images[0] || 'https://via.placeholder.com/200x120' }}
        style={styles.cardImage}
      />
      <View style={styles.cardContent}>
        <Text style={styles.cardTitle} numberOfLines={1}>
          {tour.name}
        </Text>
        <Text style={styles.cardSubtitle} numberOfLines={1}>
          {tour.duration_days} jour{tour.duration_days > 1 ? 's' : ''}
        </Text>
        <View style={styles.cardFooter}>
          <View style={styles.ratingContainer}>
            <Ionicons name="star" size={14} color={COLORS.accent} />
            <Text style={styles.ratingText}>{tour.rating.toFixed(1)}</Text>
          </View>
          {tour.eco_friendly && (
            <Ionicons name="leaf" size={14} color={COLORS.primary} />
          )}
        </View>
      </View>
    </>
  );

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text>Chargement...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Hero Header with Background */}
        <ImageBackground
          source={{ uri: 'https://images.unsplash.com/photo-1539650116574-75c0c6d73f6e?w=800' }}
          style={styles.heroBackground}
          imageStyle={styles.heroImage}
        >
          <ExpoLinearGradient
            colors={['rgba(196, 30, 58, 0.8)', 'rgba(0, 168, 107, 0.6)']}
            style={styles.heroOverlay}
          >
            <View style={styles.header}>
              <Text style={styles.welcomeText}>🇹🇳 Ahlan wa Sahlan</Text>
              <Text style={styles.appTitle}>{APP_CONFIG.name}</Text>
              <Text style={styles.subtitle}>✨ {APP_CONFIG.description} ✨</Text>
              <View style={styles.headerStats}>
                <View style={styles.statItem}>
                  <Text style={styles.statNumber}>24</Text>
                  <Text style={styles.statLabel}>Gouvernorats</Text>
                </View>
                <View style={styles.statItem}>
                  <Text style={styles.statNumber}>100+</Text>
                  <Text style={styles.statLabel}>Artisans</Text>
                </View>
                <View style={styles.statItem}>
                  <Text style={styles.statNumber}>50+</Text>
                  <Text style={styles.statLabel}>Sites</Text>
                </View>
              </View>
            </View>
          </ExpoLinearGradient>
        </ImageBackground>

        {/* Quick Actions */}
        {renderQuickActions()}

        {/* Featured Artisans */}
        {renderFeaturedSection(
          '🎨 Artisans Recommandés',
          featuredArtisans,
          (artisan) => navigation.navigate('ArtisanDetail', { artisanId: artisan.id }),
          renderArtisanCard
        )}

        {/* Featured Sites */}
        {renderFeaturedSection(
          '🌿 Sites Éco-Touristiques',
          featuredSites,
          (site) => navigation.navigate('SiteDetail', { siteId: site.id }),
          renderSiteCard
        )}

        {/* Featured Tours */}
        {renderFeaturedSection(
          '🗺️ Circuits Populaires',
          featuredTours,
          (tour) => navigation.navigate('TourDetail', { tourId: tour.id }),
          renderTourCard
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  scrollView: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F8F9FA',
  },
  heroBackground: {
    height: 280,
    justifyContent: 'center',
  },
  heroImage: {
    opacity: 0.3,
  },
  heroOverlay: {
    flex: 1,
    justifyContent: 'center',
  },
  header: {
    padding: 30,
    alignItems: 'center',
  },
  welcomeText: {
    fontSize: 18,
    color: '#FFFFFF',
    marginBottom: 8,
    fontWeight: '600',
    textShadowColor: 'rgba(0,0,0,0.3)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 3,
  },
  appTitle: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 8,
    textAlign: 'center',
    textShadowColor: 'rgba(0,0,0,0.5)',
    textShadowOffset: { width: 2, height: 2 },
    textShadowRadius: 5,
  },
  subtitle: {
    fontSize: 16,
    color: '#FFFFFF',
    textAlign: 'center',
    marginBottom: 20,
    fontStyle: 'italic',
    textShadowColor: 'rgba(0,0,0,0.3)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 3,
  },
  headerStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: '100%',
    marginTop: 15,
  },
  statItem: {
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 15,
    minWidth: 80,
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FFFFFF',
    textShadowColor: 'rgba(0,0,0,0.3)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
  statLabel: {
    fontSize: 12,
    color: '#FFFFFF',
    marginTop: 2,
    textShadowColor: 'rgba(0,0,0,0.3)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
  quickActionsContainer: {
    padding: 25,
    backgroundColor: '#FFFFFF',
    marginTop: -30,
    marginHorizontal: 15,
    borderRadius: 20,
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
  },
  quickActionsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    flexWrap: 'wrap',
    marginTop: 15,
  },
  quickActionCard: {
    width: (width - 80) / 2,
    height: 120,
    marginBottom: 15,
    borderRadius: 20,
    overflow: 'hidden',
    elevation: 6,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.2,
    shadowRadius: 6,
  },
  quickActionGradient: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 15,
  },
  quickActionText: {
    fontSize: 16,
    color: '#FFFFFF',
    fontWeight: 'bold',
    marginTop: 8,
    textAlign: 'center',
    textShadowColor: 'rgba(0,0,0,0.3)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
  quickActionSubtext: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.9)',
    marginTop: 2,
    textAlign: 'center',
    fontStyle: 'italic',
  },
  featuredSection: {
    marginBottom: 30,
    backgroundColor: '#FFFFFF',
    marginHorizontal: 15,
    borderRadius: 20,
    padding: 20,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 6,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: COLORS.text,
    textShadowColor: 'rgba(0,0,0,0.1)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
  seeAllText: {
    fontSize: 16,
    color: COLORS.primary,
    fontWeight: '600',
    textDecorationLine: 'underline',
  },
  featuredCard: {
    width: 220,
    backgroundColor: '#FFFFFF',
    borderRadius: 18,
    marginRight: 15,
    elevation: 6,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.05)',
  },
  cardImage: {
    width: '100%',
    height: 140,
    borderTopLeftRadius: 18,
    borderTopRightRadius: 18,
  },
  cardContent: {
    padding: 16,
    backgroundColor: '#FFFFFF',
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: COLORS.text,
    marginBottom: 6,
    textShadowColor: 'rgba(0,0,0,0.1)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 1,
  },
  cardSubtitle: {
    fontSize: 15,
    color: COLORS.textSecondary,
    marginBottom: 8,
    fontWeight: '500',
  },
  cardFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 8,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.accent,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  ratingText: {
    fontSize: 13,
    color: '#FFFFFF',
    marginLeft: 4,
    fontWeight: 'bold',
  },
  locationText: {
    fontSize: 13,
    color: COLORS.primary,
    fontWeight: '600',
  },
});

export default HomeScreen;
