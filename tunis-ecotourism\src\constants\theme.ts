import { COLORS } from './index';

// Gradients thématiques tunisiens
export const GRADIENTS = {
  // Gradients principaux
  primary: [COLORS.primary, '#E91E63'],
  secondary: [COLORS.secondary, '#4CAF50'],
  accent: [COLORS.accent, '#FFC107'],
  
  // Gradients naturels
  oasis: ['#00A86B', '#4CAF50', '#8BC34A'],
  desert: ['#FF8F00', '#FFC107', '#FFEB3B'],
  mediterranean: ['#0077BE', '#2196F3', '#03DAC6'],
  sunset: ['#FF5722', '#FF9800', '#FFC107'],
  
  // Gradients artisanaux
  pottery: ['#8D6E63', '#A1887F', '#BCAAA4'],
  weaving: ['#9C27B0', '#E91E63', '#F06292'],
  jewelry: ['#FFD700', '#FFC107', '#FF8F00'],
  leather: ['#795548', '#8D6E63', '#A1887F'],
  
  // Gradients d'ambiance
  morning: ['#E3F2FD', '#BBDEFB', '#90CAF9'],
  evening: ['#FCE4EC', '#F8BBD9', '#F48FB1'],
  night: ['#1A237E', '#303F9F', '#3F51B5'],
  
  // Gradients de statut
  success: ['#4CAF50', '#8BC34A'],
  warning: ['#FF9800', '#FFC107'],
  error: ['#F44336', '#E91E63'],
  info: ['#2196F3', '#03DAC6'],
};

// Ombres et élévations
export const SHADOWS = {
  small: {
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  medium: {
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 4,
  },
  large: {
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
  },
  xlarge: {
    elevation: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.3,
    shadowRadius: 12,
  },
};

// Rayons de bordure
export const BORDER_RADIUS = {
  small: 8,
  medium: 12,
  large: 16,
  xlarge: 20,
  round: 25,
  circle: 50,
};

// Espacements
export const SPACING = {
  xs: 4,
  sm: 8,
  md: 12,
  lg: 16,
  xl: 20,
  xxl: 24,
  xxxl: 32,
};

// Typographie
export const TYPOGRAPHY = {
  h1: {
    fontSize: 32,
    fontWeight: 'bold' as const,
    lineHeight: 40,
  },
  h2: {
    fontSize: 28,
    fontWeight: 'bold' as const,
    lineHeight: 36,
  },
  h3: {
    fontSize: 24,
    fontWeight: 'bold' as const,
    lineHeight: 32,
  },
  h4: {
    fontSize: 20,
    fontWeight: '600' as const,
    lineHeight: 28,
  },
  h5: {
    fontSize: 18,
    fontWeight: '600' as const,
    lineHeight: 24,
  },
  h6: {
    fontSize: 16,
    fontWeight: '600' as const,
    lineHeight: 22,
  },
  body1: {
    fontSize: 16,
    fontWeight: '400' as const,
    lineHeight: 24,
  },
  body2: {
    fontSize: 14,
    fontWeight: '400' as const,
    lineHeight: 20,
  },
  caption: {
    fontSize: 12,
    fontWeight: '400' as const,
    lineHeight: 16,
  },
  button: {
    fontSize: 16,
    fontWeight: 'bold' as const,
    lineHeight: 20,
  },
};

// Effets de texte
export const TEXT_SHADOWS = {
  light: {
    textShadowColor: 'rgba(0,0,0,0.1)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 1,
  },
  medium: {
    textShadowColor: 'rgba(0,0,0,0.3)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
  strong: {
    textShadowColor: 'rgba(0,0,0,0.5)',
    textShadowOffset: { width: 2, height: 2 },
    textShadowRadius: 4,
  },
};

// Animations
export const ANIMATIONS = {
  timing: {
    fast: 150,
    normal: 300,
    slow: 500,
  },
  easing: {
    easeInOut: 'ease-in-out',
    easeIn: 'ease-in',
    easeOut: 'ease-out',
    linear: 'linear',
  },
};

// Opacités
export const OPACITY = {
  disabled: 0.5,
  overlay: 0.8,
  backdrop: 0.6,
  subtle: 0.1,
  light: 0.2,
  medium: 0.4,
};

// Tailles d'icônes
export const ICON_SIZES = {
  xs: 16,
  sm: 20,
  md: 24,
  lg: 32,
  xl: 40,
  xxl: 48,
};

// Thème complet
export const THEME = {
  colors: COLORS,
  gradients: GRADIENTS,
  shadows: SHADOWS,
  borderRadius: BORDER_RADIUS,
  spacing: SPACING,
  typography: TYPOGRAPHY,
  textShadows: TEXT_SHADOWS,
  animations: ANIMATIONS,
  opacity: OPACITY,
  iconSizes: ICON_SIZES,
};

export default THEME;
