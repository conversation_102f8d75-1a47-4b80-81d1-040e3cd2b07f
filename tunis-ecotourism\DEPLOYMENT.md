# Guide de Déploiement - Tunis Éco-Tourisme

Ce guide vous explique comment déployer l'application Tunis Éco-Tourisme sur différentes plateformes.

## 📋 Prérequis

- Compte Supabase configuré avec la base de données
- Compte Expo (pour le déploiement mobile)
- Compte Vercel ou Netlify (pour le déploiement web)
- Comptes développeur Apple/Google (pour les stores)

## 🗄️ Configuration de la Base de Données

### 1. Supabase Setup

1. Créer un nouveau projet sur [Supabase](https://supabase.com)
2. Dans l'éditeur SQL, exécuter le script `database/init.sql`
3. Optionnel : Exécuter `database/sample_data.sql` pour les données de test
4. Configurer l'authentification :
   - Aller dans Authentication > Settings
   - Activer "Enable email confirmations"
   - Configurer les templates d'email
   - Ajouter les URLs de redirection

### 2. Variables d'Environnement

Copier `.env.example` vers `.env` et configurer :

```bash
cp .env.example .env
```

Mettre à jour les valeurs dans `.env` :
- `EXPO_PUBLIC_SUPABASE_URL` : URL de votre projet Supabase
- `EXPO_PUBLIC_SUPABASE_ANON_KEY` : Clé anonyme de Supabase

## 📱 Déploiement Mobile

### 1. Configuration Expo

```bash
# Installer Expo CLI globalement
npm install -g @expo/cli

# Se connecter à Expo
expo login

# Configurer le projet
expo install
```

### 2. Build de Développement

```bash
# Build pour Android
expo build:android

# Build pour iOS (nécessite macOS)
expo build:ios
```

### 3. Publication sur Expo

```bash
# Publier sur Expo Go
expo publish

# Ou créer une build de production
expo build:android --type apk
expo build:ios --type archive
```

### 4. Déploiement sur les Stores

#### Google Play Store
1. Créer un compte développeur Google Play
2. Générer un APK signé avec `expo build:android --type apk`
3. Uploader l'APK sur Google Play Console
4. Remplir les métadonnées de l'app
5. Soumettre pour révision

#### Apple App Store
1. Créer un compte développeur Apple
2. Générer un IPA avec `expo build:ios --type archive`
3. Uploader via Xcode ou Application Loader
4. Configurer dans App Store Connect
5. Soumettre pour révision

## 🌐 Déploiement Web

### 1. Build Web

```bash
# Créer le build web
expo build:web

# Ou utiliser la commande export
expo export:web
```

### 2. Déploiement sur Vercel

```bash
# Installer Vercel CLI
npm install -g vercel

# Déployer
vercel --prod
```

Configuration `vercel.json` :
```json
{
  "name": "tunis-ecotourisme",
  "builds": [
    {
      "src": "package.json",
      "use": "@vercel/static-build",
      "config": {
        "distDir": "web-build"
      }
    }
  ],
  "routes": [
    {
      "src": "/(.*)",
      "dest": "/index.html"
    }
  ]
}
```

### 3. Déploiement sur Netlify

```bash
# Build
npm run build:web

# Déployer via Netlify CLI
npm install -g netlify-cli
netlify deploy --prod --dir=web-build
```

Configuration `netlify.toml` :
```toml
[build]
  command = "expo export:web"
  publish = "web-build"

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200
```

## 🔧 Configuration de Production

### 1. Variables d'Environnement de Production

Créer `.env.production` :
```bash
EXPO_PUBLIC_SUPABASE_URL=https://your-prod-project.supabase.co
EXPO_PUBLIC_SUPABASE_ANON_KEY=your-prod-anon-key
EXPO_PUBLIC_APP_ENV=production
```

### 2. Configuration App.json

Mettre à jour `app.json` pour la production :
```json
{
  "expo": {
    "name": "Tunis Éco-Tourisme",
    "slug": "tunis-ecotourisme",
    "version": "1.0.0",
    "orientation": "portrait",
    "icon": "./assets/icon.png",
    "splash": {
      "image": "./assets/splash-icon.png",
      "resizeMode": "contain",
      "backgroundColor": "#2E7D32"
    },
    "updates": {
      "fallbackToCacheTimeout": 0
    },
    "assetBundlePatterns": [
      "**/*"
    ],
    "ios": {
      "supportsTablet": true,
      "bundleIdentifier": "com.tunisecotourisme.app"
    },
    "android": {
      "adaptiveIcon": {
        "foregroundImage": "./assets/adaptive-icon.png",
        "backgroundColor": "#2E7D32"
      },
      "package": "com.tunisecotourisme.app"
    },
    "web": {
      "favicon": "./assets/favicon.png"
    }
  }
}
```

## 🚀 Scripts de Déploiement

Ajouter dans `package.json` :
```json
{
  "scripts": {
    "deploy:web": "expo export:web && vercel --prod",
    "deploy:android": "expo build:android --type apk",
    "deploy:ios": "expo build:ios --type archive",
    "publish": "expo publish"
  }
}
```

## 📊 Monitoring et Analytics

### 1. Supabase Analytics
- Activer les métriques dans le dashboard Supabase
- Configurer les alertes pour les erreurs

### 2. Expo Analytics
- Utiliser `expo-analytics` pour tracker l'usage
- Configurer Google Analytics pour le web

### 3. Error Tracking
```bash
# Installer Sentry
expo install @sentry/react-native

# Configurer dans App.tsx
import * as Sentry from '@sentry/react-native';

Sentry.init({
  dsn: 'YOUR_SENTRY_DSN',
});
```

## 🔒 Sécurité

### 1. Variables Sensibles
- Ne jamais commiter les fichiers `.env`
- Utiliser des secrets pour les clés API
- Configurer Row Level Security sur Supabase

### 2. HTTPS
- Forcer HTTPS en production
- Configurer les headers de sécurité

### 3. Authentification
- Configurer les politiques de mot de passe
- Activer la vérification email
- Implémenter la 2FA si nécessaire

## 📝 Checklist de Déploiement

- [ ] Base de données configurée et migrée
- [ ] Variables d'environnement définies
- [ ] Tests passés
- [ ] Build de production créé
- [ ] Métadonnées de l'app complétées
- [ ] Icônes et splash screens configurés
- [ ] Politiques de confidentialité et CGU ajoutées
- [ ] Analytics configurés
- [ ] Monitoring en place
- [ ] Backup de la base de données configuré

## 🆘 Dépannage

### Erreurs Communes

1. **Build Failed** : Vérifier les dépendances et la compatibilité des versions
2. **Supabase Connection** : Vérifier les URLs et clés API
3. **Metro Bundle Error** : Nettoyer le cache avec `expo start --clear`
4. **Web Build Issues** : S'assurer que toutes les dépendances sont compatibles web

### Support

- Documentation Expo : https://docs.expo.dev/
- Documentation Supabase : https://supabase.com/docs
- Issues GitHub : [Lien vers votre repo]
- Email support : <EMAIL>
