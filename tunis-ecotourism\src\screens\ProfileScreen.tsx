import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  Image,
  ImageBackground,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { COLORS } from '../constants';
import StatsCard from '../components/StatsCard';

const ProfileScreen: React.FC = () => {
  const profileOptions = [
    { title: 'Mes Favoris', icon: 'heart', color: [COLORS.primary, '#E91E63'] },
    { title: 'Mes Réservations', icon: 'calendar', color: [COLORS.secondary, '#4CAF50'] },
    { title: 'Mes Avis', icon: 'star', color: [COLORS.accent, '#FFC107'] },
    { title: 'Paramètres', icon: 'settings', color: [COLORS.nature.earth, '#8D6E63'] },
    { title: 'Aide & Support', icon: 'help-circle', color: ['#9C27B0', '#673AB7'] },
    { title: 'À propos', icon: 'information-circle', color: ['#607D8B', '#455A64'] },
  ];

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Header avec photo de profil */}
        <ImageBackground
          source={{ uri: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=800' }}
          style={styles.headerBackground}
          imageStyle={styles.headerImage}
        >
          <LinearGradient
            colors={['rgba(196, 30, 58, 0.8)', 'rgba(0, 168, 107, 0.6)']}
            style={styles.headerOverlay}
          >
            <View style={styles.profileHeader}>
              <View style={styles.avatarContainer}>
                <Image
                  source={{ uri: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=200' }}
                  style={styles.avatar}
                />
                <TouchableOpacity style={styles.editAvatarButton}>
                  <Ionicons name="camera" size={20} color="#FFFFFF" />
                </TouchableOpacity>
              </View>

              <Text style={styles.userName}>Ahmed Ben Salem</Text>
              <Text style={styles.userEmail}><EMAIL></Text>

              <View style={styles.userBadge}>
                <Ionicons name="star" size={16} color={COLORS.accent} />
                <Text style={styles.badgeText}>Explorateur Éco</Text>
              </View>
            </View>
          </LinearGradient>
        </ImageBackground>

        {/* Statistiques */}
        <View style={styles.statsContainer}>
          <View style={styles.statsRow}>
            <StatsCard
              title="Lieux Visités"
              value="12"
              icon="location"
              gradientColors={[COLORS.primary, '#E91E63']}
            />
            <StatsCard
              title="Artisans Rencontrés"
              value="8"
              icon="hammer"
              gradientColors={[COLORS.secondary, '#4CAF50']}
            />
          </View>
          <View style={styles.statsRow}>
            <StatsCard
              title="Circuits Complétés"
              value="3"
              icon="map"
              gradientColors={[COLORS.accent, '#FFC107']}
            />
            <StatsCard
              title="Avis Donnés"
              value="15"
              icon="star"
              gradientColors={[COLORS.nature.earth, '#8D6E63']}
            />
          </View>
        </View>

        {/* Options du profil */}
        <View style={styles.optionsContainer}>
          {profileOptions.map((option, index) => (
            <TouchableOpacity key={index} style={styles.optionCard}>
              <LinearGradient
                colors={option.color}
                style={styles.optionGradient}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
              >
                <View style={styles.optionContent}>
                  <View style={styles.optionIcon}>
                    <Ionicons name={option.icon as any} size={24} color="#FFFFFF" />
                  </View>
                  <Text style={styles.optionTitle}>{option.title}</Text>
                  <Ionicons name="chevron-forward" size={20} color="rgba(255,255,255,0.8)" />
                </View>
              </LinearGradient>
            </TouchableOpacity>
          ))}
        </View>

        {/* Bouton de déconnexion */}
        <TouchableOpacity style={styles.logoutButton}>
          <LinearGradient
            colors={['#FF5722', '#F44336']}
            style={styles.logoutGradient}
          >
            <Ionicons name="log-out" size={24} color="#FFFFFF" />
            <Text style={styles.logoutText}>Se déconnecter</Text>
          </LinearGradient>
        </TouchableOpacity>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  headerBackground: {
    height: 280,
  },
  headerImage: {
    opacity: 0.3,
  },
  headerOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  profileHeader: {
    alignItems: 'center',
    paddingHorizontal: 30,
  },
  avatarContainer: {
    position: 'relative',
    marginBottom: 20,
  },
  avatar: {
    width: 120,
    height: 120,
    borderRadius: 60,
    borderWidth: 4,
    borderColor: '#FFFFFF',
  },
  editAvatarButton: {
    position: 'absolute',
    bottom: 5,
    right: 5,
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: COLORS.primary,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#FFFFFF',
  },
  userName: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 8,
    textAlign: 'center',
    textShadowColor: 'rgba(0,0,0,0.5)',
    textShadowOffset: { width: 2, height: 2 },
    textShadowRadius: 5,
  },
  userEmail: {
    fontSize: 16,
    color: 'rgba(255,255,255,0.9)',
    marginBottom: 15,
    textShadowColor: 'rgba(0,0,0,0.3)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
  userBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  badgeText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 6,
    textShadowColor: 'rgba(0,0,0,0.3)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
  statsContainer: {
    padding: 20,
    marginTop: -30,
  },
  statsRow: {
    flexDirection: 'row',
    marginBottom: 15,
  },
  optionsContainer: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  optionCard: {
    marginBottom: 15,
    borderRadius: 20,
    overflow: 'hidden',
    elevation: 6,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.2,
    shadowRadius: 6,
  },
  optionGradient: {
    padding: 20,
  },
  optionContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  optionIcon: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 20,
  },
  optionTitle: {
    flex: 1,
    fontSize: 18,
    fontWeight: '600',
    color: '#FFFFFF',
    textShadowColor: 'rgba(0,0,0,0.3)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
  logoutButton: {
    marginHorizontal: 20,
    marginBottom: 40,
    borderRadius: 20,
    overflow: 'hidden',
    elevation: 6,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.2,
    shadowRadius: 6,
  },
  logoutGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 18,
  },
  logoutText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginLeft: 12,
    textShadowColor: 'rgba(0,0,0,0.3)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
});

export default ProfileScreen;
