{"name": "tunis-ecotourism", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@react-navigation/bottom-tabs": "^7.4.7", "@react-navigation/native": "^7.1.17", "@react-navigation/stack": "^7.4.8", "@supabase/supabase-js": "^2.57.0", "expo": "~53.0.22", "expo-status-bar": "~2.2.3", "react": "19.0.0", "react-native": "0.79.6", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "expo-location": "~18.1.6", "expo-camera": "~16.1.11", "expo-media-library": "~17.1.7", "react-native-gesture-handler": "~2.24.0", "react-native-reanimated": "~3.17.4", "expo-av": "~15.1.7", "@react-native-picker/picker": "2.11.1", "react-dom": "19.0.0", "react-native-web": "^0.20.0", "@expo/metro-runtime": "~5.0.4", "expo-linear-gradient": "~14.1.5"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "typescript": "~5.8.3"}, "private": true}