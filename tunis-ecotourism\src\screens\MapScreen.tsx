import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
  ScrollView,
  Image,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { COLORS, GOVERNORATES } from '../constants';
import { GRADIENTS } from '../constants/theme';

const MapScreen: React.FC = () => {
  const [selectedGovernorate, setSelectedGovernorate] = useState<string | null>(null);
  const [mapView, setMapView] = useState<'regions' | 'sites' | 'artisans'>('regions');

  const governorateData = [
    { name: 'Tunis', sites: 15, artisans: 25, color: GRADIENTS.primary },
    { name: 'Nabe<PERSON>', sites: 12, artisans: 18, color: GRADIENTS.pottery },
    { name: 'Kairouan', sites: 8, artisans: 22, color: GRADIENTS.weaving },
    { name: 'Tozeur', sites: 6, artisans: 8, color: GRADIENTS.oasis },
    { name: 'Sfax', sites: 10, artisans: 15, color: GRADIENTS.mediterranean },
    { name: 'B<PERSON><PERSON>', sites: 9, artisans: 12, color: GRADIENTS.secondary },
  ];

  const renderMapControls = () => (
    <View style={styles.controlsContainer}>
      <ScrollView horizontal showsHorizontalScrollIndicator={false}>
        <TouchableOpacity
          style={[styles.controlButton, mapView === 'regions' && styles.activeControl]}
          onPress={() => setMapView('regions')}
        >
          <LinearGradient
            colors={mapView === 'regions' ? GRADIENTS.primary : ['#FFFFFF', '#F5F5F5']}
            style={styles.controlGradient}
          >
            <Ionicons
              name="map"
              size={20}
              color={mapView === 'regions' ? '#FFFFFF' : COLORS.textSecondary}
            />
            <Text style={[
              styles.controlText,
              { color: mapView === 'regions' ? '#FFFFFF' : COLORS.textSecondary }
            ]}>
              Régions
            </Text>
          </LinearGradient>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.controlButton, mapView === 'sites' && styles.activeControl]}
          onPress={() => setMapView('sites')}
        >
          <LinearGradient
            colors={mapView === 'sites' ? GRADIENTS.secondary : ['#FFFFFF', '#F5F5F5']}
            style={styles.controlGradient}
          >
            <Ionicons
              name="location"
              size={20}
              color={mapView === 'sites' ? '#FFFFFF' : COLORS.textSecondary}
            />
            <Text style={[
              styles.controlText,
              { color: mapView === 'sites' ? '#FFFFFF' : COLORS.textSecondary }
            ]}>
              Sites
            </Text>
          </LinearGradient>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.controlButton, mapView === 'artisans' && styles.activeControl]}
          onPress={() => setMapView('artisans')}
        >
          <LinearGradient
            colors={mapView === 'artisans' ? GRADIENTS.accent : ['#FFFFFF', '#F5F5F5']}
            style={styles.controlGradient}
          >
            <Ionicons
              name="hammer"
              size={20}
              color={mapView === 'artisans' ? '#FFFFFF' : COLORS.textSecondary}
            />
            <Text style={[
              styles.controlText,
              { color: mapView === 'artisans' ? '#FFFFFF' : COLORS.textSecondary }
            ]}>
              Artisans
            </Text>
          </LinearGradient>
        </TouchableOpacity>
      </ScrollView>
    </View>
  );

  const renderGovernorateCard = (gov: any, index: number) => (
    <TouchableOpacity
      key={index}
      style={styles.governorateCard}
      onPress={() => setSelectedGovernorate(gov.name)}
    >
      <LinearGradient
        colors={gov.color}
        style={styles.governorateGradient}
      >
        <View style={styles.governorateContent}>
          <Text style={styles.governorateName}>{gov.name}</Text>
          <View style={styles.governorateStats}>
            <View style={styles.statItem}>
              <Ionicons name="location" size={16} color="rgba(255,255,255,0.9)" />
              <Text style={styles.statText}>{gov.sites} sites</Text>
            </View>
            <View style={styles.statItem}>
              <Ionicons name="hammer" size={16} color="rgba(255,255,255,0.9)" />
              <Text style={styles.statText}>{gov.artisans} artisans</Text>
            </View>
          </View>
        </View>
        <View style={styles.cardShine} />
      </LinearGradient>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <LinearGradient
        colors={GRADIENTS.mediterranean}
        style={styles.header}
      >
        <Text style={styles.headerTitle}>🗺️ Carte Interactive</Text>
        <Text style={styles.headerSubtitle}>
          Explorez la Tunisie et découvrez ses trésors
        </Text>
      </LinearGradient>

      {/* Contrôles de la carte */}
      {renderMapControls()}

      {/* Carte simulée */}
      <View style={styles.mapContainer}>
        <Image
          source={{ uri: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800' }}
          style={styles.mapImage}
        />
        <LinearGradient
          colors={['transparent', 'rgba(0,0,0,0.3)']}
          style={styles.mapOverlay}
        >
          <Text style={styles.mapPlaceholder}>
            🌍 Carte interactive en cours de développement
          </Text>
          <Text style={styles.mapSubtext}>
            Bientôt disponible avec géolocalisation en temps réel
          </Text>
        </LinearGradient>
      </View>

      {/* Liste des gouvernorats */}
      <View style={styles.governoratesContainer}>
        <Text style={styles.sectionTitle}>📍 Gouvernorats Populaires</Text>
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.governoratesList}
        >
          {governorateData.map(renderGovernorateCard)}
        </ScrollView>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  header: {
    paddingHorizontal: 25,
    paddingVertical: 30,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 8,
    textShadowColor: 'rgba(0,0,0,0.5)',
    textShadowOffset: { width: 2, height: 2 },
    textShadowRadius: 5,
  },
  headerSubtitle: {
    fontSize: 16,
    color: 'rgba(255,255,255,0.9)',
    textAlign: 'center',
    textShadowColor: 'rgba(0,0,0,0.3)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
  controlsContainer: {
    paddingHorizontal: 20,
    paddingVertical: 15,
    backgroundColor: '#FFFFFF',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  controlButton: {
    marginRight: 15,
    borderRadius: 20,
    overflow: 'hidden',
  },
  activeControl: {
    elevation: 6,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.2,
    shadowRadius: 6,
  },
  controlGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 12,
  },
  controlText: {
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 8,
  },
  mapContainer: {
    flex: 1,
    margin: 20,
    borderRadius: 20,
    overflow: 'hidden',
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
  },
  mapImage: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  mapOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    top: 0,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 30,
  },
  mapPlaceholder: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FFFFFF',
    textAlign: 'center',
    marginBottom: 10,
    textShadowColor: 'rgba(0,0,0,0.7)',
    textShadowOffset: { width: 2, height: 2 },
    textShadowRadius: 5,
  },
  mapSubtext: {
    fontSize: 16,
    color: 'rgba(255,255,255,0.9)',
    textAlign: 'center',
    textShadowColor: 'rgba(0,0,0,0.5)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 3,
  },
  governoratesContainer: {
    backgroundColor: '#FFFFFF',
    paddingVertical: 20,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: COLORS.text,
    marginBottom: 15,
    paddingHorizontal: 20,
    textShadowColor: 'rgba(0,0,0,0.1)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 1,
  },
  governoratesList: {
    paddingHorizontal: 15,
  },
  governorateCard: {
    width: 200,
    height: 120,
    marginHorizontal: 5,
    borderRadius: 20,
    overflow: 'hidden',
    elevation: 6,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.2,
    shadowRadius: 6,
  },
  governorateGradient: {
    flex: 1,
    padding: 20,
    justifyContent: 'space-between',
    position: 'relative',
  },
  governorateContent: {
    flex: 1,
    justifyContent: 'space-between',
  },
  governorateName: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 10,
    textShadowColor: 'rgba(0,0,0,0.5)',
    textShadowOffset: { width: 2, height: 2 },
    textShadowRadius: 4,
  },
  governorateStats: {
    flexDirection: 'column',
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  statText: {
    fontSize: 14,
    color: 'rgba(255,255,255,0.9)',
    marginLeft: 8,
    fontWeight: '500',
    textShadowColor: 'rgba(0,0,0,0.3)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
  cardShine: {
    position: 'absolute',
    top: -15,
    right: -15,
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    transform: [{ rotate: '45deg' }],
  },
});

export default MapScreen;
