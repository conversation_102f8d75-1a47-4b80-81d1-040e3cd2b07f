# Learn more https://docs.github.com/en/get-started/getting-started-with-git/ignoring-files

# Dependencies
node_modules/

# Expo
.expo/
dist/
web-build/
expo-env.d.ts

# Native
.kotlin/
*.orig.*
*.jks
*.p8
*.p12
*.key
*.mobileprovision

# Metro
.metro-health-check*

# Debug
npm-debug.*
yarn-debug.*
yarn-error.*

# macOS
.DS_Store
*.pem

# Local env files
.env*.local
.env

# Typescript
*.tsbuildinfo

# IDE
.vscode/
.idea/
*.swp
*.swo

# Logs
logs
*.log

# Temporary folders
tmp/
temp/

# OS generated files
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/
*.lnk
