import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  SafeAreaView,
  FlatList,
  Image,
  ImageBackground,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { COLORS, GOVERNORATES, TOUR_THEMES } from '../constants';
import { Artisan, EcoSite, Tour, SearchFilters } from '../types';
import { getArtisans, getEcoSites, getTours } from '../services/supabase';

interface ExploreScreenProps {
  navigation: any;
}

const ExploreScreen: React.FC<ExploreScreenProps> = ({ navigation }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [activeTab, setActiveTab] = useState<'all' | 'artisans' | 'sites' | 'tours'>('all');
  const [filters, setFilters] = useState<SearchFilters>({
    query: '',
    type: 'all',
  });
  const [results, setResults] = useState<{
    artisans: Artisan[];
    sites: EcoSite[];
    tours: Tour[];
  }>({
    artisans: [],
    sites: [],
    tours: [],
  });
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    loadData();
  }, []);

  useEffect(() => {
    handleSearch();
  }, [searchQuery, activeTab]);

  const loadData = async () => {
    try {
      setLoading(true);
      const [artisansResult, sitesResult, toursResult] = await Promise.all([
        getArtisans(),
        getEcoSites(),
        getTours(),
      ]);

      setResults({
        artisans: artisansResult.data || [],
        sites: sitesResult.data || [],
        tours: toursResult.data || [],
      });
    } catch (error) {
      console.error('Error loading data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = () => {
    // TODO: Implement search functionality
    console.log('Searching for:', searchQuery, 'in', activeTab);
  };

  const renderTabButton = (tab: typeof activeTab, title: string, icon: string) => (
    <TouchableOpacity
      style={[styles.tabButton, activeTab === tab && styles.activeTabButton]}
      onPress={() => setActiveTab(tab)}
    >
      {activeTab === tab ? (
        <LinearGradient
          colors={[COLORS.primary, '#E91E63']}
          style={styles.activeTabGradient}
        >
          <Ionicons name={icon as any} size={22} color="#FFFFFF" />
          <Text style={styles.activeTabButtonText}>{title}</Text>
        </LinearGradient>
      ) : (
        <>
          <Ionicons name={icon as any} size={20} color={COLORS.textSecondary} />
          <Text style={styles.tabButtonText}>{title}</Text>
        </>
      )}
    </TouchableOpacity>
  );

  const renderArtisanItem = ({ item }: { item: Artisan }) => (
    <TouchableOpacity
      style={styles.itemCard}
      onPress={() => navigation.navigate('ArtisanDetail', { artisanId: item.id })}
    >
      <Image
        source={{ uri: item.images[0] || 'https://via.placeholder.com/100x100' }}
        style={styles.itemImage}
      />
      <View style={styles.itemContent}>
        <Text style={styles.itemTitle}>{item.name}</Text>
        <Text style={styles.itemSubtitle}>{item.specialty}</Text>
        <Text style={styles.itemLocation}>{item.governorate}</Text>
        <View style={styles.itemFooter}>
          <View style={styles.ratingContainer}>
            <Ionicons name="star" size={14} color={COLORS.accent} />
            <Text style={styles.ratingText}>{item.rating.toFixed(1)}</Text>
          </View>
          {item.workshop_visits_available && (
            <View style={styles.badge}>
              <Text style={styles.badgeText}>Visite atelier</Text>
            </View>
          )}
        </View>
      </View>
    </TouchableOpacity>
  );

  const renderSiteItem = ({ item }: { item: EcoSite }) => (
    <TouchableOpacity
      style={styles.itemCard}
      onPress={() => navigation.navigate('SiteDetail', { siteId: item.id })}
    >
      <Image
        source={{ uri: item.images[0] || 'https://via.placeholder.com/100x100' }}
        style={styles.itemImage}
      />
      <View style={styles.itemContent}>
        <Text style={styles.itemTitle}>{item.name}</Text>
        <Text style={styles.itemSubtitle}>{item.type}</Text>
        <Text style={styles.itemLocation}>{item.governorate}</Text>
        <View style={styles.itemFooter}>
          <View style={styles.ratingContainer}>
            <Ionicons name="star" size={14} color={COLORS.accent} />
            <Text style={styles.ratingText}>{item.rating.toFixed(1)}</Text>
          </View>
          {item.vr_supported && (
            <View style={styles.badge}>
              <Text style={styles.badgeText}>VR</Text>
            </View>
          )}
        </View>
      </View>
    </TouchableOpacity>
  );

  const renderTourItem = ({ item }: { item: Tour }) => (
    <TouchableOpacity
      style={styles.itemCard}
      onPress={() => navigation.navigate('TourDetail', { tourId: item.id })}
    >
      <Image
        source={{ uri: item.images[0] || 'https://via.placeholder.com/100x100' }}
        style={styles.itemImage}
      />
      <View style={styles.itemContent}>
        <Text style={styles.itemTitle}>{item.name}</Text>
        <Text style={styles.itemSubtitle}>
          {item.duration_days} jour{item.duration_days > 1 ? 's' : ''}
        </Text>
        <Text style={styles.itemLocation}>
          {item.governorates.slice(0, 2).join(', ')}
          {item.governorates.length > 2 && '...'}
        </Text>
        <View style={styles.itemFooter}>
          <View style={styles.ratingContainer}>
            <Ionicons name="star" size={14} color={COLORS.accent} />
            <Text style={styles.ratingText}>{item.rating.toFixed(1)}</Text>
          </View>
          {item.eco_friendly && (
            <View style={styles.badge}>
              <Text style={styles.badgeText}>Éco</Text>
            </View>
          )}
        </View>
      </View>
    </TouchableOpacity>
  );

  const getFilteredData = () => {
    switch (activeTab) {
      case 'artisans':
        return results.artisans.filter(item =>
          item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          item.specialty.toLowerCase().includes(searchQuery.toLowerCase())
        );
      case 'sites':
        return results.sites.filter(item =>
          item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          item.type.toLowerCase().includes(searchQuery.toLowerCase())
        );
      case 'tours':
        return results.tours.filter(item =>
          item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          item.themes.some(theme => theme.toLowerCase().includes(searchQuery.toLowerCase()))
        );
      default:
        return [
          ...results.artisans.slice(0, 3),
          ...results.sites.slice(0, 3),
          ...results.tours.slice(0, 3),
        ];
    }
  };

  const renderItem = ({ item }: { item: any }) => {
    if (item.specialty) return renderArtisanItem({ item });
    if (item.type) return renderSiteItem({ item });
    if (item.duration_days) return renderTourItem({ item });
    return null;
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header with Search Bar */}
      <ImageBackground
        source={{ uri: 'https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=800' }}
        style={styles.headerBackground}
        imageStyle={styles.headerImage}
      >
        <LinearGradient
          colors={['rgba(0, 168, 107, 0.9)', 'rgba(196, 30, 58, 0.7)']}
          style={styles.headerOverlay}
        >
          <View style={styles.searchContainer}>
            <Text style={styles.headerTitle}>🔍 Explorer la Tunisie</Text>
            <View style={styles.searchBar}>
              <Ionicons name="search" size={22} color={COLORS.primary} />
              <TextInput
                style={styles.searchInput}
                placeholder="Rechercher des lieux, artisans..."
                placeholderTextColor={COLORS.textSecondary}
                value={searchQuery}
                onChangeText={setSearchQuery}
              />
              {searchQuery.length > 0 && (
                <TouchableOpacity onPress={() => setSearchQuery('')}>
                  <Ionicons name="close-circle" size={22} color={COLORS.textSecondary} />
                </TouchableOpacity>
              )}
            </View>
          </View>
        </LinearGradient>
      </ImageBackground>

      {/* Tabs */}
      <View style={styles.tabsContainer}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          {renderTabButton('all', 'Tout', 'grid-outline')}
          {renderTabButton('artisans', 'Artisans', 'hammer-outline')}
          {renderTabButton('sites', 'Sites', 'location-outline')}
          {renderTabButton('tours', 'Circuits', 'map-outline')}
        </ScrollView>
      </View>

      {/* Results */}
      <FlatList
        data={getFilteredData()}
        renderItem={renderItem}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContainer}
        showsVerticalScrollIndicator={false}
        refreshing={loading}
        onRefresh={loadData}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  headerBackground: {
    height: 140,
  },
  headerImage: {
    opacity: 0.3,
  },
  headerOverlay: {
    flex: 1,
    justifyContent: 'center',
  },
  searchContainer: {
    paddingHorizontal: 25,
    paddingVertical: 20,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FFFFFF',
    textAlign: 'center',
    marginBottom: 20,
    textShadowColor: 'rgba(0,0,0,0.5)',
    textShadowOffset: { width: 2, height: 2 },
    textShadowRadius: 5,
  },
  searchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderRadius: 25,
    paddingHorizontal: 20,
    paddingVertical: 15,
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: COLORS.text,
    marginLeft: 15,
    fontWeight: '500',
  },
  tabsContainer: {
    paddingHorizontal: 20,
    marginBottom: 20,
    marginTop: 10,
  },
  tabButton: {
    marginRight: 15,
    borderRadius: 25,
    overflow: 'hidden',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  activeTabButton: {
    elevation: 6,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.2,
    shadowRadius: 6,
  },
  activeTabGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 12,
  },
  tabButtonText: {
    fontSize: 14,
    color: COLORS.textSecondary,
    marginLeft: 8,
    fontWeight: '500',
    backgroundColor: COLORS.surface,
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 25,
  },
  activeTabButtonText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
    marginLeft: 8,
    textShadowColor: 'rgba(0,0,0,0.3)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
  listContainer: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  itemCard: {
    flexDirection: 'row',
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    marginBottom: 15,
    padding: 15,
    elevation: 6,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.05)',
  },
  itemImage: {
    width: 90,
    height: 90,
    borderRadius: 15,
  },
  itemContent: {
    flex: 1,
    marginLeft: 15,
    justifyContent: 'space-between',
  },
  itemTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: COLORS.text,
    marginBottom: 6,
    textShadowColor: 'rgba(0,0,0,0.1)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 1,
  },
  itemSubtitle: {
    fontSize: 15,
    color: COLORS.textSecondary,
    marginBottom: 6,
    fontWeight: '500',
  },
  itemLocation: {
    fontSize: 13,
    color: COLORS.primary,
    marginBottom: 10,
    fontWeight: '600',
  },
  itemFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.accent,
    paddingHorizontal: 10,
    paddingVertical: 5,
    borderRadius: 15,
  },
  ratingText: {
    fontSize: 13,
    color: '#FFFFFF',
    marginLeft: 4,
    fontWeight: 'bold',
  },
  badge: {
    backgroundColor: COLORS.secondary,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 15,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  badgeText: {
    fontSize: 11,
    color: '#FFFFFF',
    fontWeight: 'bold',
    textShadowColor: 'rgba(0,0,0,0.3)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 1,
  },
});

export default ExploreScreen;
